VITE_APP_TITLE = 'ub-s09k'
VITE_APP_PORT = 9000

VITE_UNI_APPID = '__UNI__D1E5001'
VITE_WX_APPID = 'wx7c6672de1ff17fd0'

# h5部署网站的base，配置到 manifest.config.ts 里的 h5.router.base
# https://uniapp.dcloud.net.cn/collocation/manifest.html#h5-router
VITE_APP_PUBLIC_BASE=/

# 后台请求地址
VITE_SERVER_BASEURL = 'https://ukw0y1.laf.run'
# 后台上传地址
VITE_UPLOAD_BASEURL = 'https://ukw0y1.laf.run/upload'

# 注意，如果是微信小程序，还有一套请求地址的配置，在 `src/utils/index.ts` 中

# h5是否需要配置代理
VITE_APP_PROXY_ENABLE = true
VITE_APP_PROXY_PREFIX = '/api'
# 后端是否有统一前缀 /api，决定本地代码的时候是否需要去掉 /api 前缀。这里面默认是没有的，即前端会把/api 转发去掉
VITE_SERVER_HAS_API_PREFIX = false

# 第二个请求地址 (目前alova中可以使用)
VITE_API_SECONDARY_URL = 'https://ukw0y1.laf.run'

# 认证模式，'single' | 'double' ==> 单token | 双token
VITE_AUTH_MODE = 'single'
