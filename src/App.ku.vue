<script setup lang="ts">
import { ref } from 'vue'
import { useThemeStore } from '@/store'
import CTTabBar from '@/tabbar/index.vue'
import { isPageTabBar } from './tabbar/store'
import { currRoute } from './utils'

const themeStore = useThemeStore()

const isCurrentPageTabBar = ref(true)
onShow(() => {
  console.log('App.ku.vue onShow', currRoute())
  const { path } = currRoute()
  isCurrentPageTabBar.value = isPageTabBar(path)
})

const helloKuRoot = ref('Hello AppKuVue')

const exposeRef = ref('this is form app.Ku.vue')

defineExpose({
  exposeRef,
})
</script>

<template>
  <wd-config-provider :theme-vars="themeStore.themeVars" :theme="themeStore.theme">
    <!-- 这个先隐藏了，知道这样用就行 -->
    <view class="hidden text-center">
      {{ helloKuRoot }}，这里可以配置全局的东西
    </view>

    <KuRootView />

    <CTTabBar v-if="isCurrentPageTabBar" />
    <wd-toast />
    <wd-message-box />
  </wd-config-provider>
</template>
