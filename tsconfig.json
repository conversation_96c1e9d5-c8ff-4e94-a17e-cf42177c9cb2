{
  "compilerOptions": {
    "composite": true,
    "target": "es2015",
    "lib": ["esnext", "dom"],
    "baseUrl": ".",
    "module": "ESNext",
    "moduleResolution": "Node",
    "paths": {
      "@/*": ["./src/*"],
      "@img/*": ["./src/static/*"]
    },
    "resolveJsonModule": true,
    "types": [
      "@dcloudio/types",
      "@uni-helper/uni-types",
      "@uni-helper/vite-plugin-uni-pages",
      "miniprogram-api-typings",
      "wot-design-uni/global.d.ts",
      "z-paging/types",
      "./src/typings.d.ts"
    ],

    // ==================== 开发者友好配置 ====================
    // 允许 JavaScript 和 TypeScript 混合开发
    "allowJs": true,

    // 关闭严格模式，降低类型检查严格程度
    "strict": false,

    // 允许不可达代码
    "allowUnreachableCode": true,

    // 允许未使用的标签
    "allowUnusedLabels": true,
    // 允许隐式 any 类型，不强制类型注解
    "noImplicitAny": false,

    // 不强制所有代码路径都有返回值
    "noImplicitReturns": false,
    // 允许隐式 this 类型
    "noImplicitThis": false,

    // 允许未使用的局部变量
    "noUnusedLocals": false,

    // 允许未使用的参数
    "noUnusedParameters": false,

    // 输出配置
    "outDir": "dist",
    "sourceMap": true,
    // 允许从没有默认导出的模块中默认导入
    "allowSyntheticDefaultImports": true,

    // 模块解析配置
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": false,
    // 跳过库文件类型检查，提升编译速度
    "skipLibCheck": true
  },
  "vueCompilerOptions": {
    "plugins": ["@uni-helper/uni-types/volar-plugin"]
  },
  "include": [
    "package.json",
    "src/**/*.ts",
    "src/**/*.js",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.jsx",
    "src/**/*.vue",
    "src/**/*.json"
  ],
  "exclude": ["node_modules", "dist"]
}
